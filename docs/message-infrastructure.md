# 数据规范



## 通用请求格式 (设备上报 / 服务响应)

```json
{
    "id": "1234567890", // 唯一消息ID (建议UUID或时间戳+序列号)，平台响应/ACK会带回此ID
    "method": "event.{event_identifier}", // String类型，标识操作类型 (见下表)
    "params": { ... },  // Object类型，具体参数内容，由物模型定义决定
}
```



## 通用响应格式 (平台对设备请求的ACK / 服务调用指令)

```json
{
    "id": "1234567890", // String类型，**必须与对应请求的`id`一致**
    "method": "event.{event_identifier}" // String类型 (服务指令特有)，标识服务类型
    "code": 0,        // Int类型，响应码 (0表示成功，其他见错误码表)
    "data": { ... },    // Object类型，响应数据内容
}
```



## 关键 `method` 值对照表

| 操作类型           | `method` 值 (示例)           | 方向         | 说明                 |
| :----------------- | :--------------------------- | :----------- | :------------------- |
| **获取属性值**     | `property.get`               | 平台 -> 设备 | 请求上报属性         |
| **属性主动上报**   | `property.report`            | 设备 -> 平台 | 主动上报一组属性值   |
| **设置属性值**     | `property.set`               | 平台 -> 设备 | 平台设置设备属性     |
| **事件上报**       | `event.{event_identifier}`   | 设备 -> 平台 | 上报一个特定事件     |
| **服务调用指令**   | `action.{action_identifier}` | 平台 -> 设备 | 平台要求设备执行服务 |
| **设置影子期望值** | desired.set                  | 平台 -> 设备 | 设置影子期望值       |
| **获取影子期望值** | desired.get                  | 设备 -> 平台 | 获取影子期望值       |



## `params` / `data` 内容规则

- **属性 (Property):** `params` 中为键值对，键是属性标识符 (Identifier)，值是其对应的数据值 (符合TSL定义的数据类型和范围)。

    - *属性上报示例 (*`params`):

      ```json
      "params": {
          "Temperature": 25.6,
          "Humidity": 45,
          "PowerSwitch": 1
      }
      ```

- **事件 (Event):** `params` 中包含 `value` 对象，其内部键是事件输出参数的标识符。

    - *事件上报示例 (*`params`):

      ```json
      "params": {
          "value": {
              "ErrorCode": 1001,
              "ErrorMsg": "Sensor failure"
          }
      }
      ```

- **服务 (Action):**

    - **指令 (**`method` 为 `action.{action_identifier}`): `data` 对象包含服务输入参数的键值对。

      ```json
      "data": {
          "TargetTemperature": 22.0
      }
      ```

    - **响应 (**`method` 为 `action.{action_identifier}`): `data` 对象包含服务输出参数的键值对 (服务定义中有输出时)。

      ```json
      "data": {
          "ActualTemperature": 21.8
      }
      ```