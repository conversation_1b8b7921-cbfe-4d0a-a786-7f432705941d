# 物模型

## 概念

物模型（Thing Model）在平台中指对物理设备的数字模型，是实现物理设备与数字世界的互联互通的统一的 “数字说明书”，它是由设备的属性、服务、事件等组成的一种结构化描述。
从技术本质来看，物模型采取JSON格式描述。通过抽象设备的属性（如温度、开关状态）、方法（如启动、调节参数）、事件（如故障报警、任务完成），构建出可被程序识别的数字模型。例如，一个智能温湿度传感器的物模型会包含 “temperature”（温度属性，单位℃）、“humidity”（湿度属性，单位 %）、“report_data”（数据上报方法）、“low_battery”（低电量事件）等要素。


## 作用

物模型设计有以下几个作用：
1. 提高开发效率：物模型设计可以将不同设备之间的通信和数据交换规范化，减少开发人员的工作量。
2. 提高系统的可扩展性：物模型设计可以在系统中引入新的设备类型，对于不同的设备类型，可以快速实现数据的交换和管理。
3. 提高系统的可维护性：物模型设计可以将设备的特征和行为进行抽象和规范化，使得系统的架构更加清晰明了，方便维护和升级。



## 核心构成

● 属性（Property）：作为设备实时状态的直观呈现，用户根据企业内产品尽可能采用统一命名。例如所有设备的开关状态均用 “power” 表示，取值为布尔值 true/false，这种标准化命名极大地提升了设备间的兼容性与用户操作的便捷性。

● 方法（Action）：物模型中的方法旨在封装复杂操作逻辑，以提升设备控制的效率与便捷性。所有方法的参数都与已定义的属性紧密关联，确保操作与设备状态的实时同步。

● 事件（Event）：事件是设备主动向外界上报自身瞬时状态变化的关键途径，所有事件参数均需引用已定义属性，以保证信息的准确性与可追溯性。



## 属性



### 控制端请求设备上报最新属性

**流程**

```
设备订阅
云端下发property/${productId}/${deviceId}/down  get
设备端回复属性property/${productId}/${deviceId}/up   reply
```



method值

| 主题方向 |                 |                |                            |
| -------- | --------------- | -------------- | -------------------------- |
| down     | property.get    | 获取属性值     |                            |
| up       | property.get    | 获取属性值回复 | 业务端查询后，记录最新值   |
| down     | property.set    | 设置属性值     |                            |
| up       | property.set    | 设置属性值回复 |                            |
| up       | property.report | 设备上报属性值 | 设备状态变化时，记录最新值 |
|          |                 |                |                            |

注：以上所有消息均按需（产品规则编排中）存放至DB，供查看数据使用，默认最多存放60天



**请求数据格式**：

```json
{
    "version": "1.0",
    "id": "abc123",
    "method": "property.get",
    "params": {
        "fields": [
            "mac",
            "variableVoltageA",
            "variableCurA",
            "variableFreq",
            "variableInsPT",
            "variablePwrFT",
            "variableInsST",
            "energyEpT",
            "FaultStatus",
            "Reclose"
        ]
    }
}
```



**回复数据格式**：

*成功返回*

```json
{
    "id": "abc123",
    "code": 0,
    "method": "property.get",
    "data": {
        "mac": "C2A82A05BF51",
        "variableVoltageA": 220.1,
        "variableCurA": 5.67,
        "variableFreq": 49.9,
        "variableInsPT": 69,
        "variablePwrFT": 0.85,
        "variableInsST": 256,
        "energyEpT": 9763,
        "FaultStatus": 0,
        "Reclose": {
            "FinalState": 0,
            "overCur_OCT": 200,
            "overCur_OCRT": 10,
            "MiddleTimeout": 20,
            "EndTimeout": 30,
            "ReclosingNumber": 0,
            "SamplingCount": 20
        }
    }
}
```



*失败返回*

```json
{
    "id": "abc123",
    "code": 4321,
    "method": "property.get",
    "data": {}
}
```



### 设备端属性变化主动上报

流程

```
设备状态变化后立即上报
设备端回复属性property/${productId}/${deviceId}/up   report
平台端回复 property/${productId}/${deviceId}/down   reply
```

上报数据格式

```json
{
  "id": "abc123",
  "method":"property.report",
  "data": {
      "mac": "C2A82A05BF51",
      "variableVoltageA": 220.1,
      "variableCurA": 5.67,
      "variableFreq": 49.9,
      "variableInsPT": 69,
      "variablePwrFT": 0.85,
      "variableInsST": 256,
      "energyEpT": 9763,
      "FaultStatus": 0,      
      "reclosureStatus": 4
  }
}
```

平台回复数据格式：

成功

```json
{
    "id": "abc123",
    "code": 0,
    "method": "property.report",
    "data": {},
}
```

失败

```json
{
    "id": "abc123",
    "code": 4321,
    "method": "property.report",
    "data": {}
}
```



### 平台端下发属性设置

**流程**

```
设备订阅
云端下发property/${productId}/${deviceId}/down
设备端回复属性property/${productId}/${deviceId}/up
```

**下发格式**

```json
{
    "id": "abc123",
    "method": "property.set",
    "params": {
        "mac": "C2A82A05BF51",
        "variableVoltageA": 220.1,
        "variableCurA": 5.67,
        "variableFreq": 49.9,
        "variableInsPT": 69,
        "variablePwrFT": 0.85,
        "variableInsST": 256,
        "energyEpT": 9763,
        "FaultStatus": 0,
        "Reclose": {
            "FinalState": 0,
            "overCur_OCT": 200,
            "overCur_OCRT": 10,
            "MiddleTimeout": 20,
            "EndTimeout": 30,
            "ReclosingNumber": 0,
            "SamplingCount": 20
        }
    }
}
```



**设备回复数据格式**

*成功*

```json
{
  "id": "abc123",
  "code": 0,
  "method": "property.set"
  "data": {
    "max_current": 20,
    "auto_shutdown": true
  }
}
```

*失败*

```json
{
    "id": "abc123",
    "code": 6813,
    "method": "property.set",
    "data": {}
}
```



## 事件

流程

```
流程图
```

上报消息数据格式

```json
{
    "id": "abc123",
    "method": "event.${event.identifier}",
    "ack": 0,
    "params": {
        "Power": "on",
        "WF": "2"
    }
}
```

回复数据格式

成功

```json
{
    "id": "abc123",
    "code": 0,
    "method": "event.${event.identifier}",
    "data": {}
}
```

失败

```json
{
    "id": "abc123",
    "code": 4321,
    "method": "event.${event.identifier}",
    "data": {}
}
```



## 行为

**流程**

```

```

发下数据格式

```json
{
    "id": "abc123",
    "method": "action.${action.identifier}"
    "params": {
        "Power": "on",
        "WF": "2"
    },
}
```

回复数据格式

成功

```json
{
    "id": "abd123",
    "method": "action.${action.identifier}",
    "code": 0,
    "data": {}
}
```

失败

```json
{
    "id": "abd123",
    "method": "action.${action.identifier}",
    "code": 4321,
    "data": {}
}
```

