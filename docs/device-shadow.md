# 6.2 设备影子



### 6.2.1 概念

设备影子(Device Shadow)是设备在云端的虚拟映射，用来记录设备的最近状态和预期状态，通过设备影子可以轻松实现云端对设备状态的管控。设备影子是一个JSON文档，存储了设备的当前状态、属性和配置信息，使得即使设备处于离线状态或无法直接访问时，应用程序和服务仍然能够与之进行交互。每个设备都拥有且仅拥有一个独特的设备影子，由设备ID进行唯一标识，设备可以通过MQTT获取和设置设备影子来同步状态，该同步可以是影子同步给设备，也可以是设备同步给影子。



### 6.2.2 设备影子的组成

设备影子采用json格式存储，最外层只能包含四个字段，分别是reported、desired、timestamp、version。

| 属性          | 描述                                                         |      |
| ------------- | ------------------------------------------------------------ | ---- |
| **reported**  | 设备的报告状态。设备可以在reported部分写入数据，报告其最新状态。应用程序可以通过读取该参数值，获取设备的状态。JSON文档中也可以不包含reported部分，没有reported部分的文档同样为有效影子JSON文档。 |      |
| **desired**   | 设备的预期状态。仅当设备影子文档具有预期状态时，才包含desired部分。应用程序向desired部分写入数据，更新事物的状态，而无需直接连接到该设备。 |      |
| **timestamp** | 影子文档的最新更新时间。                                     |      |
| **version**   | 用户主动更新版本号时，设备影子会检查请求中的**version**值是否大于当前版本号。如果大于当前版本号，则更新设备影子，并将**version**值更新到请求的版本中，反之则会拒绝更新设备影子。该参数更新后，版本号会递增，用于确保正在更新的文档为最新版本。version参数为long型。为防止参数溢出，您可以手动传入`-1`将版本号重置为`0`。 |      |

其限制条件如下：

- 属性名称不能以@开头，且其中不能包含dollar（$），dot（.），space（ ），以及comma（,）符号，<=64bytes
- 属性值支持bool、int32、int64、float、double、string，不支持null和array，对于string类型<=512bytes
- 对于reported，desired每个存储体嵌套层级<=5
- 每个JsonObject层级中key值<=50



### 6.2.3 工作原理

设备影子通过MQTT等协议与设备进行双向通信，实现了状态的同步与更新。当设备状态发生变化时，它会通过MQTT将最新的状态信息上报给云端，并更新到设备影子中。同样，当应用程序需要修改设备状态时，它会将新的状态信息写入设备影子，并等待设备上线后同步更新。



### 6.2.4 使用场景

#### 场景1：网络不稳定，设备频繁上下线。

由于网络不稳定，设备频繁上下线。应用程序发出需要获取当前的设备状态请求时，设备掉线，无法获取设备状态，但下一秒设备又连接成功，应用程序无法正确发起请求。

使用设备影子机制存储设备最新状态，一旦设备状态产生变化，设备会将状态同步到设备影子。应用程序只需要请求或订阅推送方式获取影子中的状态即可，不需要关心设备是否在线。

#### 场景2：多程序同时请求获取设备状态。

如果设备网络稳定，很多应用程序请求获取设备状态，设备需要根据请求响应多次，即使响应的结果是一样的，设备本身处理能力有限，无法负载被请求多次的情况。

使用设备影子机制，设备只需要主动同步状态给设备影子一次，多个应用程序请求或订阅推送方式，获取设备影子中存储的设备状态，即可获取设备最新状态，做到应用程序和设备的解耦。

#### 场景3：设备掉线。

设备网络不稳定，导致设备频繁上下线，应用程序发送控制指令给设备时，设备掉线，指令无法下达到设备。通过QoS=1或者2实现，但是该方法对于服务端的压力比较大，一般不建议使用。

使用设备影子机制，应用程序发送控制指令，指令携带时间戳保存在设备影子中。当设备掉线重连时，获取指令并根据时间戳确定是否执行。

设备连接掉线，指令发送失败。设备再上线时，设备影子功能通过指令加时间戳的模式，保证设备不会执行过期指令。

#### 场景4：设备状态比对通知

设备上报状态时，仅需报告变更的部分；此时应用对发生变化的属性值更为关心。反之应用对设备亦然。

应用或设备更新属性（desired/reported）后，设备或应用可获取差异推送delta。

#### 场景5：设备初始配置信息获取

设备首次连接时，需要一些配置项或参数作为初始化配置，一般可将配置信息写入固件，但具有差异化的配置就较难处理。

使用设备影子机制，可以将一般性的配置写入影子模板，以此模板创建设备时模板内容将作为设备初始版本的影子。若针对特定设备变更初始配置，也可以针对性更新其影子，设备首次连接时进行 get 获取影子，即可获取期望配置。



### 6.2.5 设备端



#### 6.2.5.1 设备影子主题

平台已为每个设备内置了两个系统Topic，用于实现设备影子数据流转。

> `shadow/${productId}/${deviceId}/desired`

设备订阅此Topic获取最新期望值消息，应用程序发布消息到此Topic后，如果设备在线通过该Topic能立即收到消息，不论是否在线进入该Topic的消息后，均会将消息中的期望值更新到设备影子中。

>`shadow/${productId}/${deviceId}/reported`

设备影子更新状态到该Topic，平台收到数据后会将消息更新影子的reported区中。

方法类别

|                 |                    |                                    |
| --------------- | ------------------ | ---------------------------------- |
| desired.set     | 设置期望值         | 设备在线                           |
| desired.get     | 设备主动获取期望值 | 设备上线后发送该指令获取影子期望值 |
| shadow.reported | 上报属性值         |                                    |



#### 6.2.5.2 设备收到期望值设置

流程图



设置数据

```json
{
    "id":"abc123",
    "method":"desired.set",
    "data":{
        "power":1,
        "temperature":28.1
    }
}
```



消息回复

成功

```json
{
    "id":"abc123",
    "method":"desired.set",
    "code":0,
    "data":{}
}
```



失败

```json
{
    "id":"abc123",
    "method":"desired.set",
    "code":4321,
    "data":{}
}
```





#### 6.2.5.3 主动获取设备期望值

流程图



数据上行

```json
{
    "id" : "abc123",
    "method":"desired.get",
    "params" : [
        "power",
        "temperature"
    ]
}
```

数据回报

成功消息

```json
{
    "id":"abc123",
    "method":"desired.get",
    "code":0,
    "data":{
        "power":1,
        "temperature":28.1
    }
}
```

失败消息

```json
{
    "id":"abc123",
    "method":"shadow.get",
    "code":4321,
    "data":{}
}
```



#### 6.2.5.4 上报设备属性值

流程图



数据上行

```json
{
    "id" : "abc123",
    "method":"shadow.reported",
    "params" : {
        "power":true,
        "temperature":23.6
    }
}
```

数据回报

成功消息

```json
{
    "id":"abc123",
    "method":"shadow.reported",
    "code":0,
    "data":{
    }
}
```

失败消息

```json
{
    "id":"abc123",
    "method":"shadow.reported",
    "code":4321,
    "data":{}
}
```







### 6.2.6 应用服务接口



#### 6.2.6.1 配置设备影子期望数据

云端可以调用SetShadowDesired接口，设置期望属性值（desired区）来控制设备。在云端设置设备期望属性值后，若设备在线，将实时更新设备属性状态；若设备离线，期望属性值将缓存云端，待设备上线后，获取期望属性值，并更新属性状态。



#### 6.2.6.2 查询影子数据

应用服务器可调用此接口查询指定设备的设备影子信息，包括对设备的期望属性信息（desired区）和设备最新上报的属性信息（reported区）



#### 6.2.6.3 删除设备影子

用户删除设备影子，平台将会将设备影子中的所有数据（包含上报值和期望值）清空。

