# 2.连接认证



## 2.1 连接信息

- **Broker 地址：** `mqtt://[客户平台域名].com` (例: `mqtt://nebulemq.com`)

MQTT 连接可以使用 TCP 、TLS/SSL两种方式，对应端口如下：

| 协议    | 端口 | 描述                                               |
| ------- | ---- | -------------------------------------------------- |
| TCP     | 1883 | 非加密 MQTT 连接(**测试开发使用，生产环境不推荐**) |
| TLS/SSL | 8883 | 基于 TLS 加密的 MQTT 连接(**生产环境强制要求**)    |



## 2.2 接入方式介绍

NebuleMQ平台需要每个接入的设备必须拥有平台为其分配的产品唯一标识Product ID，用户在创建产品时需要选择设备认证方式:密钥认证或证书认证，在设备接入时需要根据指定的方式上报产品、设备信息与对应的密钥信息，认证通过后设备才能成功连接NebuleMQ平台。

平台为保证设备的安全性，仅提供“一机一密”的鉴权认证方式，即强制要求每一个接入平台的设备，拥有平台颁发的唯一设备身份标识Device ID及设备密钥Device Secret，设备名称Device Name 可用户自定义，但需要保证全局唯一，建议使用MAC地址或IMEI码等设备唯一身份信息来保证其信息的合法性。由于不同用户的设备端资源、安全等级要求都不同，平台提供了两种认证模式(当前仅支持设备密钥的方式)，以满足不同的使用场景。

平台为您提供以下三种认证方案：

证书认证：为每台设备分配证书 + 私钥，使用非对称加密认证接入，平台会为每台设备烧录不同的配置信息提供烧录文件。

密钥认证：为每台设备分配设备密钥，使用对称加密认证接入，平台会为每台设备烧录不同的配置信息提供烧录文件。

动态注册认证：为同一产品下的所有设备出厂时仅烧写product_id和product_secret分配统一密钥，设备通过API注册请求获取设备证书/密钥后认证接入平台。

三种方案在易用性、安全性和对设备资源要求上各有优劣，您可以根据自己的业务场景综合评估选择。方案对比如下：

| 特性                 | 证书认证                                                | 密钥认证                             | 动态注册认证                                                 |
| -------------------- | ------------------------------------------------------- | ------------------------------------ | ------------------------------------------------------------ |
| 设备烧录信息         | product_id、device_name、设备证书、设备私钥<br />根证书 | product_id、device_id、device_secret | product_id、device_name、<br />product_secret(用于签名使用，网络无须传输) |
| 是否需要提前创建设备 | 必须                                                    | 必须                                 | 支持根据注册请求中携带的 product_id、device_name 自动创建    |
| 安全性               | 高                                                      | 一般                                 | 一般                                                         |
| 设备资源要求         | 较高，需要支持 TLS                                      | 较低                                 | 较低，支持 AES 即可                                          |



## 2.3 设备密钥认证



### 2.3.1 获取 MQTT 连接信息

在设备详情页，连接信息栏目，查看连接信息，包含device_id 和 device_secret 或证书。



### 2.3.2 生成 MQTT 用户名及密码

若创建产品时选的密钥认证，则需通过拼接获得 MQTT 连接用户名，并通过加密算法生成 MQTT 连接所需密码。

具体拼接及计算方式如下。

**client_id :**

device_id(使用平台颁发设备唯一ID)



**username：**

用户名拼接方式为 `{product_id}|{device_id}|{timestamp}|{algorithm_type}`，其中：

| 字段           | 说明                                                      |
| -------------- | --------------------------------------------------------- |
| product_id     | 产品ID                                                    |
| device_id      | 设备ID                                                    |
| timestamp      | 生成签名时的时间戳，以秒为单位，长整数。可以不用传入。    |
| algorithm_type | 字符串签名算法类型,取值 MD5 或 SHA256，不传入则默认为 MD5 |



**password：**

步骤一、组合加密字符串：`{product_id}|{device_id}|{timestamp}|{algorithm_type}{device_secret}`，其中：

| 字段           | 说明                                                         |
| -------------- | ------------------------------------------------------------ |
| product_id     | 产品ID                                                       |
| device_id      | 设备ID                                                       |
| timestamp      | 生成签名时的时间戳，以秒为单位，长整数。<br />时间戳会与服务器时间判比，最大误差允许10min；<br />若无法保证时间准确性，计算时需传入0进行占位 |
| algorithm_type | 字符串签名算法类型,取值 MD5 或 SHA256                        |
| device_secret  | 平台提供设备密钥                                             |



步骤二、进行加密

使用MD5或者SHA256，对加密字符串进行加密。具体过程如下：

获取加密字符串的UTF-8字符集比特数组，按选定的加密方式，对1中得到比特数组使用MD5或者SHA256进行加密，并将**结果转换为小写**形式。





## 2.4 设备证书认证

待补全！



## 2.5 连接参数

- **Clean Session:** `true` (建议。除非设备有可靠存储并能处理离线消息，否则选 `true` 避免堆积)。

- **Keep Alive:** `60-600` 秒 (根据设备功耗和网络状况设定)。

- **LWT (遗嘱消息):**

    - **Topic:** `status/${productId}/${deviceName}/disconnect`

    - **Payload :**

      ```json
      {
          "id": "abc123",
          "params": {
              "status": "offline"
          },
          "method": "status.post"
      }
      ```

    - **QoS:** `1`

    - **Retain:** `true` (关键！确保设备离线后平台能立即获取状态)。

