#  设备NTP服务

设备NTP 服务主要是解决资源受限的设备，系统不包含 NTP 服务，没有精确时间戳的问题。
系统内置两个 Topic来实现NTP服务：

设备对时	发布	ntp/${productId}/${deviceId}/up
设备对时	订阅	ntp/${productId}/${deviceId}/down

实现原理
物联网通信平台借鉴 NTP 协议原理，将平台作为 NTP 服务器。设备端向平台请求时，平台返回的 NTP 时间。设备端收到返回后，再结合请求时间和接收时间，一起计算出当前精确时间。
操作步骤见下图：

流程

```

```

NTP服务使用流程，及其Topic说明如下：


1. 设备端向Topic：`ntp/${productId}/${deviceId}/up`发送一条QoS=0的消息，携带设备当前的时间戳，单位为毫秒。示例如下：

   ```json
   {
       "id": "abc123",
       "method": "set",
       "params": {
           "deviceSendTime": "1571724098000"
       }
   }
   ```

   **说明**

    - 时间戳数字，支持Long（默认）和String类型。
    - NTP服务目前仅支持QoS=0的消息。

2. 设备端通过Topic：`ntp/${productId}/${deviceId}/down`，收到物联网平台回复的消息，包含以下信息。

   ```json
   {
       "id": "abc123",
       "method": "set.batch",
       "code": 0,
       "data": {
           "deviceSendTime": "1571724098000",
           "serverRecvTime": "1571724098110",
           "serverSendTime": "1571724098115"
       }
   }
   ```

3. 设备端计算出服务端当前精确的Unix时间。

   假设基于请求的时延和响应的时延相同，设备端收到服务端的时间即${deviceRecvTime}，则设备上的精确时间为：`(${serverRecvTime}+${serverSendTime}+${deviceRecvTime}-${deviceSendTime})/2`。

