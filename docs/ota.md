#  固件更新(OTA)

OTA（Over-the-Air Technology）即空中下载技术，基于无线网络对设备固件、软件或驱动进行更新。通过OTA升级，可以对物联网设备更新功能、修复漏洞、优化性能。



##  升级流程

<img src="/image-20250808145619043.png" alt="image-20250808145619043" style="zoom:20%;" />


```json
{
  "id": "abc123",
  "method": "set",
  "params": {
        "fileSign":"93230c3bde425a9d7984a594ac56****",
        "size": 278421,
        "version": "v1.0.32",
        "url": "https://the_firmware_url",
        "signMethod": "MD5",
        "md5": "f8d85b250d4d787a9f48***",
        "module": "wifi",
        "extData":{
            "key1":"value1",
            "key2":"value2"
        }
    }
}
```



