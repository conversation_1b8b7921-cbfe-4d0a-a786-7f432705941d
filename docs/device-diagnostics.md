# 6.7 设备诊断

使用Wi-Fi接入网络的设备可以主动将网络状态信息和网络错误数据，通过指定Topic上报至云端。下面介绍设备上报网络状态的Topic、数据格式和网络错误数据说明。

用于功能测试，设备自检将自检结果数据通过该功能进行上报

运行过程中可以获取网络状态信息和网络错误数据等

设备主动上报网络等检测状态	发布	diag/${productId}/${deviceId}/up
接收设备检测指令	订阅	diag/${productId}/${deviceId}/down

流程

### 平台下发诊断信息给设备

```
平台下发检查指令 diag/${productId}/${deviceId}/down
```

下发消息数据格式

```json
{
  "id": "abc123",
  "method": "get.current",
  "params": {
    "diag_type": "wifi"
  }
}
```

设备上报诊断信息

```json
{
  "id": "abd123",
  "method": "get.current",
  "data": {
      "wifi": {
        "rssi": 75,
        "snr": 20,
        "per": 10,
        "err_stats":"10,02,01;10,05,01"
      }
    }
  }
}
```

设备上报历史信息

```json
{
    "id": "123",
    "method": "get.history",
    "data": {
        "wifi": [
            {
                "rssi": 75,
                "snr": 20,
                "per": 10,
                "err_stats": "10,02,01;10,05,01"
            },
            {
                "rssi": 75,
                "snr": 20,
                "per": 10,
                "err_stats": "10,02,01;10,05,01"
            }
        ]
    }
}
```

主动上报

流程

上报数据

```json
{
  "id": "abd123",
  "method": "report.current",
  "data": {
      "wifi": {
        "rssi": 75,
        "snr": 20,
        "per": 10,
        "err_stats":"10,02,01;10,05,01"
      }
    }
  }
}
```

主动上报设备历史

```json
{
    "id": "123",
    "method": "report.history",
    "data": {
        "wifi": [
            {
                "rssi": 75,
                "snr": 20,
                "per": 10,
                "err_stats": "10,02,01;10,05,01"
            },
            {
                "rssi": 75,
                "snr": 20,
                "per": 10,
                "err_stats": "10,02,01;10,05,01"
            }
        ]
    }
}
```

